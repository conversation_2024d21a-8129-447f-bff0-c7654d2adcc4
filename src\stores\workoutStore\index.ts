/**
 * Workout store refactored into modules
 */

import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import type { WorkoutState, ExerciseSwapContext } from './types'
import type { ExerciseModel } from '@/types'
import { initialState } from './constants'
import { createCacheActions } from './cacheActions'
import { createLoadingActions } from './loadingActions'
import {
  swapExerciseService,
  SwapExerciseService,
} from '@/services/swapExerciseService'
import { logger } from '@/utils/logger'

export { type WorkoutState, type CacheStats, type CacheHealth } from './types'

export const useWorkoutStore = create<WorkoutState>()(
  // Disable devtools to reduce console noise
  // devtools(
  persist(
    (set, get) => {
      // Create cache actions
      const cacheActions = createCacheActions(set, get)

      // Create loading actions
      const loadingActions = createLoadingActions(set, get)

      return {
        ...initialState,

        setWorkout: (workout) => {
          const { workoutSession } = get()

          // Calculate initial status for each exercise based on existing session
          const exercisesWithStatus = (workout.Exercises || []).map(
            (exercise) => {
              if (!workoutSession) {
                return exercise
              }

              const sessionExercise = workoutSession.exercises.find(
                (e) => e.exerciseId === exercise.Id
              )
              const completedSets =
                sessionExercise?.sets.filter((s) => !s.isWarmup).length || 0
              const recommendation = get().getExerciseRecommendation(
                exercise.Id
              )
              const recommendedSets = recommendation?.Series || 3

              return {
                ...exercise,
                IsInProgress:
                  completedSets > 0 && completedSets < recommendedSets,
                IsFinished: completedSets >= recommendedSets,
              }
            }
          )

          set({
            currentWorkout: workout,
            exercises: exercisesWithStatus,
            currentExerciseIndex: 0,
            currentSetIndex: 0,
            error: null,
          })
        },

        startWorkout: () => {
          const { currentWorkout, exercises } = get()
          if (!currentWorkout) return

          set({
            workoutSession: {
              id: `session-${Date.now()}`,
              startTime: new Date(),
              exercises: [],
              exerciseRIRStatus: {},
            },
            // Ensure all exercises start with not started status
            exercises: exercises.map((ex) => ({
              ...ex,
              IsInProgress: false,
              IsFinished: false,
            })),
          })
        },

        nextSet: () => {
          set((state) => ({
            currentSetIndex: state.currentSetIndex + 1,
            currentSetData: {},
          }))
        },

        setCurrentSetIndex: (setIndex: number) => {
          set({
            currentSetIndex: setIndex,
            currentSetData: {},
          })
        },

        nextExercise: () => {
          const { currentExerciseIndex, exercises } = get()
          if (currentExerciseIndex < exercises.length - 1) {
            set({
              currentExerciseIndex: currentExerciseIndex + 1,
              currentSetIndex: 0,
              currentSetData: {},
            })
          }
        },

        setCurrentExerciseById: (exerciseId: number) => {
          const { exercises, currentExerciseIndex } = get()
          const exerciseIndex = exercises.findIndex(
            (ex) => Number(ex.Id) === Number(exerciseId)
          )
          if (exerciseIndex !== -1) {
            // Only reset set index if we're changing to a different exercise
            if (exerciseIndex !== currentExerciseIndex) {
              set({
                currentExerciseIndex: exerciseIndex,
                currentSetIndex: 0,
                currentSetData: {},
              })
            }
            // If it's the same exercise, don't change the set index
          }
        },

        saveSet: (setData) => {
          const { workoutSession, exercises } = get()
          if (!workoutSession) return

          const exerciseId = setData.ExerciseId
          if (!exerciseId) return // Exit if no exercise ID

          const existingExercise = workoutSession.exercises.find(
            (e) => e.exerciseId === exerciseId
          )

          // Calculate proper set number based on existing sets
          const setNumber = existingExercise
            ? existingExercise.sets.length + 1
            : 1

          const newSet = {
            setNumber,
            reps: setData.Reps,
            weight: setData.Weight,
            rir: setData.RIR,
            isWarmup: setData.IsWarmups,
            timestamp: new Date(),
          }

          if (existingExercise) {
            existingExercise.sets.push(newSet)
          } else {
            workoutSession.exercises.push({
              exerciseId,
              name: 'Exercise', // Default name, should be retrieved from exercise data
              sets: [newSet],
            })
          }

          // Update exercise status indicators
          const updatedExercises = exercises.map((exercise) => {
            if (exercise.Id === exerciseId) {
              const sessionExercise = workoutSession.exercises.find(
                (e) => e.exerciseId === exerciseId
              )
              const completedSets =
                sessionExercise?.sets.filter((s) => !s.isWarmup).length || 0

              // Get recommended sets from cached recommendations
              const { getExerciseRecommendation } = get()
              const recommendation = getExerciseRecommendation(exerciseId)
              const recommendedSets = recommendation?.Series || 3

              // Extend exercise with status and sets for UI display
              return {
                ...exercise,
                IsInProgress:
                  completedSets > 0 && completedSets < recommendedSets,
                IsFinished: completedSets >= recommendedSets,
                sets: sessionExercise?.sets || [],
                RecommendedSets: recommendedSets,
              } as ExerciseModel & {
                IsInProgress?: boolean
                IsFinished: boolean
                sets?: Array<{
                  setNumber: number
                  reps?: number
                  weight?: unknown
                  rir?: number
                  isWarmup?: boolean
                  timestamp: Date
                }>
                RecommendedSets?: number
              }
            }
            return exercise
          })

          set({
            workoutSession: { ...workoutSession },
            exercises: updatedExercises,
          })

          // Invoke callback to update UI
          const { onExerciseStatusUpdate } = get()
          if (onExerciseStatusUpdate) {
            const updatedExercise = updatedExercises.find(
              (ex) => ex.Id === exerciseId
            )
            if (updatedExercise) {
              const sessionExercise = workoutSession.exercises.find(
                (e) => e.exerciseId === exerciseId
              )
              const completedSets =
                sessionExercise?.sets.filter((s) => !s.isWarmup).length || 0
              const recommendation = get().getExerciseRecommendation(exerciseId)
              const recommendedSets = recommendation?.Series || 3

              onExerciseStatusUpdate(exerciseId, {
                exerciseId,
                isInProgress: Boolean(
                  (
                    updatedExercise as ExerciseModel & {
                      IsInProgress?: boolean
                    }
                  ).IsInProgress
                ),
                isFinished: Boolean(
                  (updatedExercise as ExerciseModel & { IsFinished?: boolean })
                    .IsFinished
                ),
                completedSets,
                recommendedSets,
              })
            }
          }
        },

        completeWorkout: () => {
          const { workoutSession } = get()
          if (!workoutSession) return

          set({
            workoutSession: {
              ...workoutSession,
              endTime: new Date(),
            },
          })
        },

        updateCurrentSet: (data) => {
          set((state) => ({
            currentSetData: {
              ...state.currentSetData,
              ...data,
            },
          }))
        },

        setLoading: (loading) => {
          set({ isLoading: loading })
        },

        setError: (error) => {
          // Set error without clearing cache
          set({ error, isLoading: false })
        },

        resetWorkout: () => {
          // Reset workout state but preserve cache
          const {
            cachedData,
            cacheStats,
            hasHydrated,
            cacheVersion,
            currentProgram,
          } = get()
          set({
            ...initialState,
            cachedData,
            cacheStats,
            hasHydrated,
            cacheVersion,
            currentProgram,
          })
        },

        clearCache: () => {
          // User-initiated cache clear only
          set({
            cachedData: initialState.cachedData,
            cacheStats: initialState.cacheStats,
          })
        },

        // Loading state management
        clearLoadingState: (exerciseId: number) => {
          set((state) => {
            const newLoadingStates = new Map(state.loadingStates)
            newLoadingStates.delete(exerciseId)
            return { loadingStates: newLoadingStates }
          })
        },

        clearAllLoadingStates: () => {
          set({ loadingStates: new Map() })
        },

        setRestTimerState: (state) => {
          set({ restTimerState: state })
        },

        // Getters
        getCurrentExercise: () => {
          const { exercises, currentExerciseIndex } = get()
          return exercises[currentExerciseIndex] || null
        },

        getCurrentSet: () => {
          const { currentSetData } = get()
          return Object.keys(currentSetData).length > 0 ? currentSetData : null
        },

        isWorkoutComplete: () => {
          const { workoutSession } = get()
          return workoutSession?.endTime !== undefined
        },

        getWorkoutDuration: () => {
          const { workoutSession } = get()
          if (!workoutSession || !workoutSession.endTime) return 0

          return (
            workoutSession.endTime.getTime() -
            workoutSession.startTime.getTime()
          )
        },

        getNextExercise: () => {
          const { exercises, currentExerciseIndex } = get()
          if (currentExerciseIndex < exercises.length - 1) {
            return exercises[currentExerciseIndex + 1] || null
          }
          return null
        },

        getRestDuration: () => {
          // Get rest duration from localStorage or use default
          if (typeof window !== 'undefined') {
            const stored = localStorage.getItem('restDuration')
            if (stored) {
              const duration = parseInt(stored, 10)
              // Validate and clamp to valid range (5s - 600s)
              if (!Number.isNaN(duration) && duration > 0) {
                return Math.max(5, Math.min(600, duration))
              }
            }
          }
          return 120 // Default 2 minutes
        },

        getExerciseProgress: () => {
          const {
            currentSetIndex,
            workoutSession,
            exercises,
            currentExerciseIndex,
          } = get()
          const currentExercise = exercises[currentExerciseIndex]

          if (!currentExercise) return null

          // Simplified exercise progress
          const totalSets = 4 // Default sets
          const completedSets = currentSetIndex
          const isFirstWorkSet = currentSetIndex === 0
          const currentSetIsWarmup = false // No warmup detection

          // Check if RIR has been captured for this exercise
          const hasRIR =
            workoutSession?.exerciseRIRStatus?.[currentExercise.Id] || false

          return {
            totalSets,
            completedSets,
            isFirstWorkSet,
            currentSetIsWarmup,
            hasRIR,
          }
        },

        updateSetRIR: (exerciseId) => {
          const { workoutSession } = get()
          if (!workoutSession) return

          // Mark that RIR has been captured for this exercise
          const updatedSession = {
            ...workoutSession,
            exerciseRIRStatus: {
              ...workoutSession.exerciseRIRStatus,
              [exerciseId]: true,
            },
          }

          set({ workoutSession: updatedSession })
        },

        // Add all cache actions
        ...cacheActions,

        // Add all loading actions
        ...loadingActions,

        // Exercise Swap Actions
        swapExercise: async (
          workoutId: number,
          sourceExerciseId: number,
          targetExerciseId: number,
          targetExercise: ExerciseModel
        ) => {
          try {
            // Execute the swap via API using template modification
            const success = await SwapExerciseService.executeSwap(
              workoutId,
              sourceExerciseId,
              targetExerciseId
            )

            if (!success) {
              throw new Error('Failed to execute exercise swap')
            }

            // Create swap context
            const sourceExercise = get().exercises.find(
              (ex) => ex.Id === sourceExerciseId
            )
            if (!sourceExercise) {
              throw new Error('Source exercise not found')
            }

            const swapContext = SwapExerciseService.createSwapContext(
              workoutId,
              sourceExercise,
              targetExercise
            )

            // Update store state
            set((state) => {
              const newSwaps = { ...state.exerciseSwaps }
              newSwaps[sourceExerciseId] = swapContext

              // Update exercises array using the service helper
              const newExercises = SwapExerciseService.replaceExerciseInArray(
                state.exercises,
                sourceExerciseId,
                targetExercise
              )

              // Also update the current workout if it exists
              let updatedWorkout = state.currentWorkout
              if (updatedWorkout && updatedWorkout.Exercises) {
                updatedWorkout = {
                  ...updatedWorkout,
                  Exercises: SwapExerciseService.replaceExerciseInArray(
                    updatedWorkout.Exercises,
                    sourceExerciseId,
                    targetExercise
                  ),
                }
              }

              return {
                exerciseSwaps: newSwaps,
                exercises: newExercises,
                currentWorkout: updatedWorkout,
              }
            })

            logger.log(
              `[WorkoutStore] Exercise swap completed: ${sourceExerciseId} -> ${targetExerciseId}`
            )
          } catch (error) {
            logger.error('[WorkoutStore] Exercise swap failed:', error)
            throw error
          }
        },

        revertExerciseSwap: async (
          workoutId: number,
          sourceExerciseId: number
        ) => {
          try {
            const { exerciseSwaps } = get()
            const swap = exerciseSwaps[sourceExerciseId]

            if (!swap) {
              throw new Error('No swap found for this exercise')
            }

            // Revert via API (swap back to original using template modification)
            const success = await SwapExerciseService.executeSwap(
              workoutId,
              swap.targetExerciseId,
              sourceExerciseId
            )

            if (!success) {
              throw new Error('Failed to revert exercise swap')
            }

            // Update store state
            set((state) => {
              const newSwaps = { ...state.exerciseSwaps }
              delete newSwaps[sourceExerciseId]

              // Create the original exercise object for reverting using stored data
              const originalExercise: ExerciseModel = {
                Id: sourceExerciseId,
                Label: swap.originalExercise.label,
                BodyPartId: swap.originalExercise.bodyPartId,
                IsSwapTarget: false,
                IsSystemExercise: swap.originalExercise.isSystemExercise,
                EquipmentId: swap.originalExercise.equipmentId || 0,
                SetStyle: swap.originalExercise.setStyle || 'Normal',
                IsBodyweight: swap.originalExercise.isBodyweight || false,
                IsFlexibility: swap.originalExercise.isFlexibility || false,
                // Default values for properties we don't store
                IsFinished: false,
                IsUnilateral: false,
                IsTimeBased: false,
                IsEasy: false,
                IsMedium: false,
                VideoUrl: '',
                IsNextExercise: false,
                IsPlate: false,
                IsWeighted: !swap.originalExercise.isBodyweight,
                IsPyramid: false,
                RepsMaxValue: 12,
                RepsMinValue: 8,
                Timer: undefined,
                IsNormalSets: true,
                WorkoutGroupId: undefined,
                IsBodypartPriority: false,
                IsOneHanded: false,
                LocalVideo: '',
                IsAssisted: false,
              }

              // Revert exercises array
              const newExercises = SwapExerciseService.replaceExerciseInArray(
                state.exercises,
                swap.targetExerciseId,
                originalExercise
              )

              // Also update the current workout if it exists
              let updatedWorkout = state.currentWorkout
              if (updatedWorkout && updatedWorkout.Exercises) {
                updatedWorkout = {
                  ...updatedWorkout,
                  Exercises: SwapExerciseService.replaceExerciseInArray(
                    updatedWorkout.Exercises,
                    swap.targetExerciseId,
                    originalExercise
                  ),
                }
              }

              return {
                exerciseSwaps: newSwaps,
                exercises: newExercises,
                currentWorkout: updatedWorkout,
              }
            })

            logger.log(
              `[WorkoutStore] Exercise swap reverted: ${sourceExerciseId}`
            )
          } catch (error) {
            logger.error('[WorkoutStore] Exercise swap revert failed:', error)
            throw error
          }
        },

        getSwapForExercise: (workoutId: number, exerciseId: number) => {
          const { exerciseSwaps } = get()
          return SwapExerciseService.getSwapForExercise(
            workoutId,
            exerciseId,
            exerciseSwaps
          )
        },

        applyExerciseSwaps: (workout) => {
          const { exerciseSwaps } = get()
          // Also apply temporary swaps from the service
          const tempSwaps = swapExerciseService.getTemporarySwaps()

          // Convert temporary swaps to the same format as persistent swaps
          const tempSwapRecord: Record<number, ExerciseSwapContext> = {}
          tempSwaps.forEach((swap) => {
            if (swap.workoutId === workout.Id) {
              tempSwapRecord[swap.sourceExerciseId] = swap
            }
          })

          // Merge persistent and temporary swaps (temporary takes precedence)
          const allSwaps = { ...exerciseSwaps, ...tempSwapRecord }

          return SwapExerciseService.applySwapsToWorkout(workout, allSwaps)
        },

        clearExerciseSwaps: () => {
          set({ exerciseSwaps: {} })
        },
      }
    },
    {
      name: 'drmuscle-workout',
      version: 3, // Increment to support exercise swaps
      migrate: (persistedState: unknown, version: number) => {
        // When version changes, return initial state to clear old cache
        if (version !== 3) {
          logger.log('[WorkoutStore] Cache version mismatch, clearing old data')
          return initialState
        }
        return persistedState
      },
      partialize: (state: WorkoutState) => ({
        workoutSession: state.workoutSession,
        exerciseSwaps: state.exerciseSwaps,
        // Limit cachedData to exclude large userWorkouts array
        cachedData: {
          ...state.cachedData,
          userWorkouts: null, // Don't persist the 547 workout templates
          // Only keep the first 10 exercise recommendations to save space
          exerciseRecommendations: Object.fromEntries(
            Object.entries(state.cachedData.exerciseRecommendations).slice(
              0,
              10
            )
          ),
        },
        cacheVersion: state.cacheVersion,
        currentProgram: state.currentProgram,
      }),
      onRehydrateStorage: () => (state: WorkoutState | undefined) => {
        if (state) {
          // Track hydration time
          const hydrationStart = performance.now()

          // Initialize missing Maps
          if (!state.loadingStates || !(state.loadingStates instanceof Map)) {
            state.loadingStates = new Map()
          }
          if (!state.errors || !(state.errors instanceof Map)) {
            state.errors = new Map()
          }

          // Initialize exerciseSwaps if missing
          if (!state.exerciseSwaps || typeof state.exerciseSwaps !== 'object') {
            state.exerciseSwaps = {}
          }

          state.setHasHydrated(true)
          // Clean expired data on hydration
          state.clearExpiredCache()
          // Update hydration time
          state.cacheStats.hydrationTime = performance.now() - hydrationStart
        }
      },
    }
  )
  // )  // Commented out devtools closing
)

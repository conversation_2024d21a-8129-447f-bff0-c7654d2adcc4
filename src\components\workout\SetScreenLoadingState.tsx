'use client'

interface SetScreenLoadingStateProps {
  exerciseName?: string
  showDetailedSkeleton?: boolean
  isLoadingRecommendations?: boolean
}

export function SetScreenLoadingState({
  exerciseName,
  showDetailedSkeleton = false,
  isLoadingRecommendations = false,
}: SetScreenLoadingStateProps) {
  // Always show detailed skeleton when loading recommendations
  const shouldShowSkeleton = showDetailedSkeleton || isLoadingRecommendations
  // Simple spinner for initial loading
  if (!shouldShowSkeleton) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[100dvh] p-4">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500" />
        <p className="mt-4 text-gray-600">
          {exerciseName
            ? `Loading ${exerciseName}...`
            : 'Loading exercise data...'}
        </p>
      </div>
    )
  }

  // Detailed skeleton that matches SetScreen layout
  return (
    <div className="min-h-[100dvh] bg-bg-primary flex flex-col">
      {/* Scrollable content area */}
      <div className="flex-1 overflow-y-auto pb-24">
        {/* Transition message for recommendations */}
        {isLoadingRecommendations && (
          <div className="px-4 py-4 bg-blue-50 border-b border-blue-200">
            <p className="text-blue-700 text-center">
              {exerciseName
                ? 'Preparing your recommendations...'
                : 'Preparing recommendations...'}
            </p>
          </div>
        )}
        {/* Set Progress Info Skeleton */}
        <div className="px-4 py-3 bg-bg-secondary border-b border-brand-primary/10">
          <div className="flex items-center justify-between">
            <div className="h-5 w-20 bg-bg-tertiary rounded animate-pulse" />
            <div className="h-5 w-16 bg-bg-tertiary rounded animate-pulse" />
          </div>
        </div>

        {/* Exercise Info Skeleton */}
        <div className="px-4 py-6 bg-bg-secondary border-b border-brand-primary/10">
          {/* Exercise name */}
          {exerciseName ? (
            <h2 className="text-2xl font-bold text-text-primary mb-2">
              {exerciseName}
            </h2>
          ) : (
            <div className="h-8 w-48 bg-bg-tertiary rounded animate-pulse mb-2" />
          )}
          {/* Target info */}
          <div className="h-5 w-32 bg-bg-tertiary rounded animate-pulse" />
        </div>

        {/* Set Inputs Skeleton */}
        <div className="px-4 py-6">
          <div className="space-y-6">
            {/* Reps input skeleton */}
            <div className="space-y-2">
              <div className="h-5 w-12 bg-bg-tertiary rounded animate-pulse" />
              <div className="flex items-center space-x-3">
                <div className="h-12 w-12 bg-bg-tertiary rounded-lg animate-pulse" />
                <div className="flex-1 h-12 bg-bg-tertiary rounded-lg animate-pulse" />
                <div className="h-12 w-12 bg-bg-tertiary rounded-lg animate-pulse" />
              </div>
            </div>

            {/* Weight input skeleton */}
            <div className="space-y-2">
              <div className="h-5 w-16 bg-bg-tertiary rounded animate-pulse" />
              <div className="flex items-center space-x-3">
                <div className="h-12 w-12 bg-bg-tertiary rounded-lg animate-pulse" />
                <div className="flex-1 h-12 bg-bg-tertiary rounded-lg animate-pulse" />
                <div className="h-12 w-12 bg-bg-tertiary rounded-lg animate-pulse" />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Fixed bottom action area skeleton */}
      <div className="fixed bottom-0 left-0 right-0 bg-bg-primary border-t border-brand-primary/10 p-4">
        <div className="max-w-lg mx-auto">
          <div className="h-12 w-full bg-brand-primary/20 rounded-lg animate-pulse" />
        </div>
      </div>
    </div>
  )
}

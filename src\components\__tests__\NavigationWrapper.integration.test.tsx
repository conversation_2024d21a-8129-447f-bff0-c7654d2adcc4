import React, { useEffect } from 'react'
import { render, screen } from '@testing-library/react'
import { NavigationWrapper } from '../NavigationWrapper'
import { useNavigation } from '@/contexts/NavigationContext'
import { usePathname } from 'next/navigation'
import { vi } from 'vitest'

// Mock next/navigation
vi.mock('next/navigation', () => ({
  usePathname: vi.fn(),
  useRouter: () => ({
    push: vi.fn(),
    back: vi.fn(),
  }),
}))

// Mock auth hook
vi.mock('@/hooks/useAuth', () => ({
  useAuth: () => ({ user: null }),
}))

// Mock workout store
vi.mock('@/stores/workoutStore', () => ({
  useWorkoutStore: () => vi.fn(),
}))

// Mock navigation config
vi.mock('@/config/navigationConfig', () => ({
  getNavigationConfig: vi.fn((pathname: string) => {
    if (pathname === '/workout') {
      return { title: 'Workout', showBackButton: false }
    }
    if (pathname.startsWith('/workout/exercise/')) {
      return { title: 'Exercise', showBackButton: true }
    }
    if (pathname === '/program') {
      return { title: 'Program', showBackButton: false }
    }
    return { title: 'Default', showBackButton: false }
  }),
}))

// Test component that simulates SetScreenWithGrid behavior
function ExerciseSimulator({ exerciseName }: { exerciseName: string }) {
  const { setTitle } = useNavigation()

  useEffect(() => {
    setTitle(exerciseName)
  }, [exerciseName, setTitle])

  return <div>Exercise: {exerciseName}</div>
}

describe('NavigationWrapper - Integration', () => {
  const mockUsePathname = usePathname as ReturnType<typeof vi.fn>

  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('should show exercise name when on exercise page, then clear when navigating away', async () => {
    // Start on exercise page
    mockUsePathname.mockReturnValue('/workout/exercise/123')

    const { rerender } = render(
      <NavigationWrapper>
        <ExerciseSimulator exerciseName="Bench Press" />
      </NavigationWrapper>
    )

    // Should show the exercise name set by the component
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent(
      'Bench Press'
    )

    // Navigation bar should NOT have large exercise styling
    const heading = screen.getByRole('heading', { level: 1 })
    expect(heading).toHaveClass('text-lg', 'font-semibold')
    expect(heading).not.toHaveClass('text-4xl')

    // Navigate to workout page
    mockUsePathname.mockReturnValue('/workout')
    rerender(
      <NavigationWrapper>
        <div>Workout Page</div>
      </NavigationWrapper>
    )

    // Should now show the static workout title, not the exercise name
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent(
      'Workout'
    )
    expect(screen.getByRole('heading', { level: 1 })).not.toHaveTextContent(
      'Bench Press'
    )
  })

  it('should update title when switching between exercises', () => {
    // Start on first exercise
    mockUsePathname.mockReturnValue('/workout/exercise/123')

    const { rerender } = render(
      <NavigationWrapper>
        <ExerciseSimulator exerciseName="Bench Press" />
      </NavigationWrapper>
    )

    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent(
      'Bench Press'
    )

    // Switch to second exercise
    mockUsePathname.mockReturnValue('/workout/exercise/456')
    rerender(
      <NavigationWrapper>
        <ExerciseSimulator exerciseName="Squats" />
      </NavigationWrapper>
    )

    // Should show new exercise name
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent(
      'Squats'
    )
    expect(screen.getByRole('heading', { level: 1 })).not.toHaveTextContent(
      'Bench Press'
    )
  })

  it('should show static title on non-exercise pages even after visiting exercise', () => {
    // Start on exercise page
    mockUsePathname.mockReturnValue('/workout/exercise/123')

    const { rerender } = render(
      <NavigationWrapper>
        <ExerciseSimulator exerciseName="Bench Press" />
      </NavigationWrapper>
    )

    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent(
      'Bench Press'
    )

    // Navigate to program page
    mockUsePathname.mockReturnValue('/program')
    rerender(
      <NavigationWrapper>
        <div>Program Page</div>
      </NavigationWrapper>
    )

    // Should show program title
    expect(screen.getByRole('heading', { level: 1 })).toHaveTextContent(
      'Program'
    )
    expect(screen.getByRole('heading', { level: 1 })).not.toHaveTextContent(
      'Bench Press'
    )
  })

  it('should use consistent navigation styling across all pages', () => {
    const pages = [
      { path: '/program', title: 'Program' },
      { path: '/workout', title: 'Workout' },
      { path: '/workout/exercise/123', title: 'Bench Press' },
    ]

    pages.forEach(({ path, title }) => {
      mockUsePathname.mockReturnValue(path)

      const { unmount } = render(
        <NavigationWrapper>
          {path.includes('exercise') ? (
            <ExerciseSimulator exerciseName={title} />
          ) : (
            <div>{title} Page</div>
          )}
        </NavigationWrapper>
      )

      const heading = screen.getByRole('heading', { level: 1 })

      // All pages should use the same standard navigation styling
      expect(heading).toHaveClass('text-lg', 'font-semibold')
      expect(heading).not.toHaveClass('text-4xl')

      // Navigation should be centered on all pages
      const navContainer = heading.closest('div')
      expect(navContainer).toHaveClass('justify-center')
      expect(navContainer).not.toHaveClass('justify-start')

      unmount()
    })
  })
})

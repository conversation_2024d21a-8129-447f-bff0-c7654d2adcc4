import { render, screen } from '@testing-library/react'
import { vi } from 'vitest'
import { ExercisePageStates } from '../ExercisePageStates'
import type { ExerciseModel } from '@/types'

// Mock the SetScreenLoadingState component
vi.mock('@/components/workout/SetScreenLoadingState', () => ({
  SetScreenLoadingState: ({
    exerciseName,
    showDetailedSkeleton,
    isLoadingRecommendations,
  }: any) => (
    <div data-testid="loading-state">
      {exerciseName && <div data-testid="exercise-name">{exerciseName}</div>}
      {showDetailedSkeleton && (
        <div data-testid="skeleton">Skeleton Loader</div>
      )}
      {isLoadingRecommendations && (
        <div data-testid="recommendations-message">
          Preparing your recommendations...
        </div>
      )}
    </div>
  ),
}))

describe('ExercisePageStates - Recommendations Loading', () => {
  const mockRetryInitialization = vi.fn()
  const mockRefetchRecommendation = vi.fn()
  const mockHandleSaveSet = vi.fn()

  const mockExercise: ExerciseModel = {
    Id: 1001,
    Label: 'Squats',
    IsBodyweight: false,
    IsEasy: false,
    IsSystemExercise: true,
    IsSwappable: true,
    IsTimeBased: false,
    IsUnilateral: false,
    VideoUrl: '',
    EquipmentModel: { Id: 1, Label: 'Barbell' },
    LastTimeModifiedBy: new Date(),
    Minutes: 3,
    Reps: 10,
    Sets: 4,
    SetsModel: {
      Series: '4',
      Reps: '10',
      SetStyle: 'Normal',
    },
    BodyPartId: 2,
    SelectedEquipmentModelId: 1,
    UnilateralOption: 0,
    SelectedUnilateralOption: 0,
  }

  it('should pass exercise name and show skeleton when loading recommendations', () => {
    render(
      <ExercisePageStates
        loadingError={null}
        workoutError={null}
        retryInitialization={mockRetryInitialization}
        isInitializing={false}
        isLoadingWorkout={false}
        isLoadingRecommendation // Loading recommendations
        isLoading={false}
        recommendation={null} // No recommendation yet
        currentExercise={mockExercise}
        workoutSession={{ id: 'test-session' }}
        error={null}
        refetchRecommendation={mockRefetchRecommendation}
        showComplete={false}
        showExerciseComplete={false}
        currentSet={null}
        isLastExercise={false}
        handleSaveSet={mockHandleSaveSet}
      />
    )

    // Should render loading state with exercise name
    expect(screen.getByTestId('loading-state')).toBeInTheDocument()
    expect(screen.getByTestId('exercise-name')).toHaveTextContent('Squats')
    expect(screen.getByTestId('skeleton')).toBeInTheDocument()
    expect(screen.getByTestId('recommendations-message')).toBeInTheDocument()
  })

  it('should show loading state without exercise name when exercise is null', () => {
    render(
      <ExercisePageStates
        loadingError={null}
        workoutError={null}
        retryInitialization={mockRetryInitialization}
        isInitializing={false}
        isLoadingWorkout={false}
        isLoadingRecommendation
        isLoading={false}
        recommendation={null}
        currentExercise={null} // No exercise
        workoutSession={{ id: 'test-session' }}
        error={null}
        refetchRecommendation={mockRefetchRecommendation}
        showComplete={false}
        showExerciseComplete={false}
        currentSet={null}
        isLastExercise={false}
        handleSaveSet={mockHandleSaveSet}
      />
    )

    // Should still show loading state but without exercise name
    expect(screen.getByTestId('loading-state')).toBeInTheDocument()
    expect(screen.queryByTestId('exercise-name')).not.toBeInTheDocument()
    expect(screen.getByTestId('skeleton')).toBeInTheDocument()
    expect(screen.getByTestId('recommendations-message')).toBeInTheDocument()
  })
})

import { render } from '@testing-library/react'
import { SetScreenLoadingState } from '../SetScreenLoadingState'

describe('SetScreenLoadingState', () => {
  it('should show skeleton loader with transition message when loading recommendations', () => {
    const { getByText, container } = render(
      <SetScreenLoadingState
        exerciseName="Bench Press"
        showDetailedSkeleton
        isLoadingRecommendations
      />
    )

    // Should show transition message
    expect(getByText('Preparing your recommendations...')).toBeInTheDocument()

    // Should show exercise name
    expect(getByText('Bench Press')).toBeInTheDocument()

    // Should show skeleton elements
    const skeletons = container.querySelectorAll('.animate-pulse')
    expect(skeletons.length).toBeGreaterThan(0)
  })

  it('should show generic message when no exercise name provided', () => {
    const { getByText } = render(
      <SetScreenLoadingState showDetailedSkeleton isLoadingRecommendations />
    )

    expect(getByText('Preparing recommendations...')).toBeInTheDocument()
  })

  it('should show simple loading when not loading recommendations', () => {
    const { getByText, container } = render(
      <SetScreenLoadingState exerciseName="Squats" />
    )

    expect(getByText('Loading Squats...')).toBeInTheDocument()

    // Should not show skeleton
    const skeletons = container.querySelectorAll('.animate-pulse')
    expect(skeletons.length).toBe(0)
  })

  it('should show default loading message when no props provided', () => {
    const { getByText } = render(<SetScreenLoadingState />)

    expect(getByText('Loading exercise data...')).toBeInTheDocument()
  })
})

import { renderHook, act, waitFor } from '@testing-library/react'
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest'
import { usePullToRefresh } from './usePullToRefresh'

describe('usePullToRefresh', () => {
  let mockElement: HTMLElement
  let touchStartEvent: TouchEvent
  let touchMoveEvent: TouchEvent
  let touchEndEvent: TouchEvent

  beforeEach(() => {
    // Create mock element
    mockElement = document.createElement('div')
    document.body.appendChild(mockElement)

    // Mock touch events
    touchStartEvent = new TouchEvent('touchstart', {
      touches: [{ clientY: 100 } as Touch],
    })

    touchMoveEvent = new TouchEvent('touchmove', {
      touches: [{ clientY: 150 } as Touch],
    })

    touchEndEvent = new TouchEvent('touchend')

    // Mock scrollTop
    Object.defineProperty(mockElement, 'scrollTop', {
      value: 0,
      writable: true,
    })
  })

  afterEach(() => {
    document.body.removeChild(mockElement)
    vi.clearAllMocks()
  })

  it('should initialize with default values', () => {
    const onRefresh = vi.fn()
    const { result } = renderHook(() => usePullToRefresh({ onRefresh }))

    expect(result.current.isRefreshing).toBe(false)
    expect(result.current.pullDistance).toBe(0)
    expect(result.current.isPulling).toBe(false)
  })

  it('should not trigger when disabled', () => {
    const onRefresh = vi.fn()
    const { result } = renderHook(() =>
      usePullToRefresh({ onRefresh, enabled: false })
    )

    act(() => {
      window.dispatchEvent(touchStartEvent)
      window.dispatchEvent(touchMoveEvent)
    })

    expect(result.current.pullDistance).toBe(0)
    expect(result.current.isPulling).toBe(false)
  })

  it('should track pull distance when pulling down', async () => {
    const onRefresh = vi.fn()
    const { result } = renderHook(() => usePullToRefresh({ onRefresh }))

    // Mock scrollY to be at top
    Object.defineProperty(window, 'scrollY', {
      value: 0,
      writable: true,
      configurable: true,
    })

    act(() => {
      window.dispatchEvent(
        new TouchEvent('touchstart', {
          touches: [{ clientY: 100 } as Touch],
        })
      )
    })

    await waitFor(() => {
      expect(result.current.isPulling).toBe(true)
    })

    act(() => {
      window.dispatchEvent(
        new TouchEvent('touchmove', {
          touches: [{ clientY: 150 } as Touch],
        })
      )
    })

    await waitFor(() => {
      expect(result.current.pullDistance).toBeGreaterThan(0)
    })

    // Clean up
    delete (window as any).scrollY
  })

  it('should trigger refresh when threshold is exceeded', async () => {
    const onRefresh = vi.fn().mockResolvedValue(undefined)
    const { result } = renderHook(() =>
      usePullToRefresh({ onRefresh, threshold: 50 })
    )

    // Mock scrollY to be at top
    Object.defineProperty(window, 'scrollY', {
      value: 0,
      writable: true,
      configurable: true,
    })

    // Start touch
    act(() => {
      window.dispatchEvent(
        new TouchEvent('touchstart', {
          touches: [{ clientY: 0 } as Touch],
        })
      )
    })

    await waitFor(() => {
      expect(result.current.isPulling).toBe(true)
    })

    // Move beyond threshold
    act(() => {
      window.dispatchEvent(
        new TouchEvent('touchmove', {
          touches: [{ clientY: 150 } as Touch],
        })
      )
    })

    await waitFor(() => {
      expect(result.current.pullDistance).toBeGreaterThan(50)
    })

    // End touch
    await act(async () => {
      window.dispatchEvent(touchEndEvent)
    })

    await waitFor(() => {
      expect(onRefresh).toHaveBeenCalled()
    })

    // Clean up
    delete (window as any).scrollY
  })

  it('should not trigger refresh when threshold is not reached', async () => {
    const onRefresh = vi.fn()
    const { result } = renderHook(() =>
      usePullToRefresh({ onRefresh, threshold: 100 })
    )

    // Start touch
    act(() => {
      window.dispatchEvent(
        new TouchEvent('touchstart', {
          touches: [{ clientY: 100 } as Touch],
        })
      )
    })

    // Move but not beyond threshold
    act(() => {
      window.dispatchEvent(
        new TouchEvent('touchmove', {
          touches: [{ clientY: 120 } as Touch],
        })
      )
    })

    // End touch
    await act(async () => {
      window.dispatchEvent(touchEndEvent)
    })

    expect(onRefresh).not.toHaveBeenCalled()
    expect(result.current.pullDistance).toBe(0)
  })

  it('should handle errors during refresh', async () => {
    const consoleError = vi.spyOn(console, 'error').mockImplementation(() => {})
    const onRefresh = vi.fn().mockRejectedValue(new Error('Refresh failed'))

    const { result } = renderHook(() =>
      usePullToRefresh({ onRefresh, threshold: 50 })
    )

    // Mock scrollY to be at top
    Object.defineProperty(window, 'scrollY', {
      value: 0,
      writable: true,
      configurable: true,
    })

    // Trigger refresh
    act(() => {
      window.dispatchEvent(
        new TouchEvent('touchstart', {
          touches: [{ clientY: 0 } as Touch],
        })
      )
    })

    await waitFor(() => {
      expect(result.current.isPulling).toBe(true)
    })

    act(() => {
      window.dispatchEvent(
        new TouchEvent('touchmove', {
          touches: [{ clientY: 100 } as Touch],
        })
      )
    })

    await act(async () => {
      window.dispatchEvent(touchEndEvent)
    })

    await waitFor(() => {
      expect(consoleError).toHaveBeenCalledWith(
        'Pull to refresh error:',
        expect.any(Error)
      )
    })

    expect(result.current.isRefreshing).toBe(false)

    consoleError.mockRestore()
    delete (window as any).scrollY
  })

  it('should only trigger when scrolled to top', () => {
    const onRefresh = vi.fn()
    const { result, rerender } = renderHook(() =>
      usePullToRefresh({ onRefresh })
    )
    // Mark as intentionally unused
    if (rerender) {
      // rerender is available but not used in this test
    }

    // Mock being scrolled down
    Object.defineProperty(window, 'scrollY', {
      value: 100,
      writable: true,
      configurable: true,
    })

    act(() => {
      window.dispatchEvent(touchStartEvent)
      window.dispatchEvent(touchMoveEvent)
    })

    expect(result.current.isPulling).toBe(false)
    expect(result.current.pullDistance).toBe(0)

    // Clean up
    delete (window as any).scrollY
  })

  it('should apply dead zone before resistance calculation', async () => {
    const onRefresh = vi.fn()
    const { result } = renderHook(() =>
      usePullToRefresh({
        onRefresh,
        threshold: 120,
        deadZone: 30,
        resistance: 4.0,
      })
    )

    // Mock scrollY to be at top
    Object.defineProperty(window, 'scrollY', {
      value: 0,
      writable: true,
      configurable: true,
    })

    // Start touch
    act(() => {
      window.dispatchEvent(
        new TouchEvent('touchstart', {
          touches: [{ clientY: 100 } as Touch],
        })
      )
    })

    // Move within dead zone (25px)
    act(() => {
      window.dispatchEvent(
        new TouchEvent('touchmove', {
          touches: [{ clientY: 125 } as Touch],
        })
      )
    })

    // Should have no pull distance within dead zone
    expect(result.current.pullDistance).toBe(0)

    // Move beyond dead zone (60px total)
    act(() => {
      window.dispatchEvent(
        new TouchEvent('touchmove', {
          touches: [{ clientY: 160 } as Touch],
        })
      )
    })

    // With deadZone 30px and resistance 4.0:
    // Effective distance = 60 - 30 = 30px
    // Resisted distance = 30 / 4.0 = 7.5px
    expect(result.current.pullDistance).toBe(7.5)

    // Clean up
    delete (window as any).scrollY
  })

  it('should require significant movement to reach 120px threshold', async () => {
    const onRefresh = vi.fn().mockResolvedValue(undefined)
    const { result } = renderHook(() =>
      usePullToRefresh({
        onRefresh,
        threshold: 120,
        deadZone: 30,
        resistance: 4.0,
      })
    )

    // Mock scrollY to be at top
    Object.defineProperty(window, 'scrollY', {
      value: 0,
      writable: true,
      configurable: true,
    })

    // Start touch
    act(() => {
      window.dispatchEvent(
        new TouchEvent('touchstart', {
          touches: [{ clientY: 100 } as Touch],
        })
      )
    })

    // To reach 120px with deadZone 30 and resistance 4.0:
    // Required movement = (120 * 4.0) + 30 = 510px
    act(() => {
      window.dispatchEvent(
        new TouchEvent('touchmove', {
          touches: [{ clientY: 610 } as Touch], // 100 + 510
        })
      )
    })

    // Should be at threshold
    expect(result.current.pullDistance).toBe(120)

    // Trigger refresh
    await act(async () => {
      window.dispatchEvent(touchEndEvent)
    })

    await waitFor(() => {
      expect(onRefresh).toHaveBeenCalled()
    })

    // Clean up
    delete (window as any).scrollY
  })
})

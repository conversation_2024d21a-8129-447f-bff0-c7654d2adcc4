'use client'

import React from 'react'
import { usePathname } from 'next/navigation'
import { GradientChevronLeftIcon } from '@/components/icons/GradientChevronLeftIcon'
import { useScrollDirection } from '@/hooks/useScrollDirection'
import { ExerciseTitle } from './ExerciseTitle'

interface IOSNavigationBarProps {
  title: string
  showBackButton?: boolean
  onBackClick?: () => void
  leftElement?: React.ReactNode
  rightElement?: React.ReactNode
  className?: string
  isExercisePage?: boolean
  setInfo?: string
  progressValue?: number
  totalSets?: number
  completedSets?: number
  titleDisplayMode?:
    | 'auto-scroll'
    | 'multiline'
    | 'swipe-scroll'
    | 'smart-truncate'
  hideOnScroll?: boolean
}

export function IOSNavigationBar({
  title,
  showBackButton = false,
  onBackClick,
  leftElement,
  rightElement,
  className = '',
  isExercisePage = false,
  setInfo,
  progressValue,
  totalSets,
  completedSets,
  titleDisplayMode = 'auto-scroll',
  hideOnScroll = true,
}: IOSNavigationBarProps) {
  const pathname = usePathname()
  const { isVisible } = useScrollDirection()

  // Hide setInfo on old exercise pages (not v2)
  const isOldExercisePage =
    pathname.includes('/workout/exercise/') &&
    !pathname.includes('/workout/exercise-v2/')
  const shouldShowSetInfo = setInfo && !isOldExercisePage
  // Determine if navigation should be hidden
  const shouldHide = hideOnScroll && !isVisible
  return (
    <>
      <header
        className={`fixed top-0 left-0 right-0 z-50 backdrop-blur-md bg-bg-primary/80 transition-transform duration-300 ease-in-out ${className}`}
        style={{
          transform: shouldHide ? 'translateY(-100%)' : 'translateY(0)',
        }}
        role="banner"
      >
        <div className="flex items-center justify-between h-[66px] px-4">
          {/* Left Section */}
          <div
            className={`${isExercisePage ? 'flex-shrink-0' : 'flex-1'} flex items-center`}
          >
            {leftElement ||
              (showBackButton && (
                <button
                  onClick={() => {
                    // Add haptic feedback
                    if ('vibrate' in navigator) {
                      navigator.vibrate(10)
                    }
                    onBackClick?.()
                  }}
                  className="flex items-center -ml-2 px-2 py-2 rounded-lg transition-colors hover:bg-bg-secondary"
                  aria-label="Go back"
                >
                  <GradientChevronLeftIcon size={isExercisePage ? 36 : 28} />
                </button>
              ))}
          </div>

          {/* Center Section - Title */}
          <div
            className={`${
              isExercisePage ? 'flex-1 min-w-0' : 'flex-1'
            } flex items-center ${
              isExercisePage ? 'justify-start' : 'justify-center'
            }`}
          >
            {isExercisePage ? (
              <div className="flex flex-col min-w-0">
                <ExerciseTitle
                  title={title}
                  titleDisplayMode={titleDisplayMode}
                  isExercisePage={isExercisePage}
                />
                {!isOldExercisePage && (
                  <div className="flex items-center gap-2 mt-1">
                    {shouldShowSetInfo && (
                      <span className="text-sm text-text-secondary">
                        {setInfo}
                      </span>
                    )}
                    {progressValue !== undefined &&
                      totalSets &&
                      completedSets !== undefined && (
                        <div
                          className="h-1 bg-surface-secondary rounded-full flex-1"
                          data-testid="nav-progress-bar"
                        >
                          <div
                            className="h-full bg-gradient-to-r from-brand-gold-start to-brand-gold-end rounded-full transition-all duration-300"
                            style={{ width: `${progressValue}%` }}
                          />
                        </div>
                      )}
                  </div>
                )}
              </div>
            ) : (
              <h1 className="text-lg font-semibold bg-gradient-to-r from-brand-gold-start to-brand-gold-end bg-clip-text text-transparent truncate">
                {title}
              </h1>
            )}
          </div>

          {/* Right Section */}
          <div
            className={`${isExercisePage ? 'flex-shrink-0' : 'flex-1'} flex items-center justify-end`}
          >
            {rightElement}
          </div>
        </div>
      </header>

      {/* Spacer to prevent content from going under fixed header */}
      <div className="h-[66px]" />
    </>
  )
}

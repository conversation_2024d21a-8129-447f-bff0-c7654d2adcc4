'use client'

import { SetScreenWithGrid } from '@/components/workout/SetScreenWithGrid'
import { SetScreenLoadingState } from '@/components/workout/SetScreenLoadingState'
import {
  ExercisePageErrorBoundary,
  WorkoutErrorBoundary,
} from '@/components/workout/ExercisePageErrorBoundary'
import { useWorkout } from '@/hooks/useWorkout'
import { useWorkoutStore } from '@/stores/workoutStore'
import { useExercisePageInitialization } from '@/hooks/useExercisePageInitialization'
import { debugLog } from '@/utils/debugLog'

interface ExercisePageClientProps {
  exerciseId: number
}

export function ExercisePageClient({ exerciseId }: ExercisePageClientProps) {
  const { isLoadingWorkout, workoutError, exercises, workoutSession } =
    useWorkout()
  const { loadingStates } = useWorkoutStore()

  const { isInitializing, loadingError, retryInitialization } =
    useExercisePageInitialization(exerciseId)

  debugLog('🏋️ [ExercisePageClient] Component rendered', {
    exerciseId,
    isLoadingWorkout,
    exercisesCount: exercises?.length || 0,
    isInitializing,
    hasLoadingError: !!loadingError,
    hasWorkoutError: !!workoutError,
    hasWorkoutSession: !!workoutSession,
  })
  // Show error state with retry option
  if (loadingError) {
    return (
      <ExercisePageErrorBoundary
        error={loadingError}
        onRetry={retryInitialization}
      />
    )
  }

  // Handle workout errors
  if (workoutError) {
    return <WorkoutErrorBoundary error={workoutError} />
  }

  // Check if we're still loading recommendations for this exercise
  const isLoadingRecommendation = exerciseId
    ? loadingStates.get(exerciseId)
    : false

  debugLog('🎨 [ExercisePageClient] Rendering decision', {
    isInitializing,
    isLoadingWorkout,
    isLoadingRecommendation,
    hasError: !!loadingError || !!workoutError,
  })

  // Find exercise name for loading state
  const exercise = exercises?.find((ex) => ex.Id === exerciseId)
  const exerciseName = exercise?.Label

  // Show loading while initializing
  if (isInitializing || isLoadingWorkout || isLoadingRecommendation) {
    return (
      <SetScreenLoadingState
        exerciseName={exerciseName}
        showDetailedSkeleton
        isLoadingRecommendations={isLoadingRecommendation}
      />
    )
  }

  // Render the SetScreenWithGrid once everything is ready
  return <SetScreenWithGrid exerciseId={exerciseId} />
}

import { describe, it, expect, vi } from 'vitest'
import { render, screen } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { IOSNavigationBar } from './IOSNavigationBar'

// Mock usePathname hook
const mockUsePathname = vi.fn(() => '/default/path')
vi.mock('next/navigation', () => ({
  usePathname: () => mockUsePathname(),
}))

describe('IOSNavigationBar', () => {
  it('renders with title', () => {
    render(<IOSNavigationBar title="Test Title" />)

    expect(screen.getByText('Test Title')).toBeInTheDocument()
  })

  describe('Exercise Page Layout', () => {
    it('should not have gold gradient background on exercise pages', () => {
      const { container } = render(
        <IOSNavigationBar title="Test Exercise" isExercisePage />
      )
      const header = container.querySelector('header')

      expect(header?.classList.contains('bg-gradient-to-r')).toBe(false)
      expect(header?.classList.contains('from-brand-gold-start')).toBe(false)
      expect(header?.classList.contains('to-brand-gold-end')).toBe(false)
    })

    it('should display exercise name in 4xl text size', () => {
      render(<IOSNavigationBar title="Test Exercise" isExercisePage />)
      const titleElement = screen.getByText('Test Exercise')

      expect(titleElement.classList.contains('text-4xl')).toBe(true)
      expect(titleElement.classList.contains('text-5xl')).toBe(false)
    })

    it('should left-align exercise name with back arrow', () => {
      render(
        <IOSNavigationBar title="Test Exercise" isExercisePage showBackButton />
      )

      const titleElement = screen.getByText('Test Exercise')
      // The title container is the parent of the parent element
      const titleContainer =
        titleElement.parentElement?.parentElement?.parentElement

      expect(titleContainer?.classList.contains('justify-start')).toBe(true)
      expect(titleContainer?.classList.contains('justify-center')).toBe(false)
    })

    it('should display set information under exercise name', () => {
      render(
        <IOSNavigationBar
          title="Test Exercise"
          isExercisePage
          setInfo="Set 3 of 5"
        />
      )

      expect(screen.getByText('Set 3 of 5')).toBeInTheDocument()
    })

    it('should handle long exercise names with ellipsis', () => {
      const longTitle =
        'This is a very long exercise name that should be truncated with ellipsis'
      render(<IOSNavigationBar title={longTitle} isExercisePage />)

      const titleElement = screen.getByText(longTitle)
      expect(titleElement.classList.contains('truncate')).toBe(true)
    })

    it('should display set info below exercise name', () => {
      render(
        <IOSNavigationBar
          title="Test Exercise"
          isExercisePage
          setInfo="Set 1 of 3"
        />
      )

      const exerciseName = screen.getByText('Test Exercise')
      const setInfo = screen.getByText('Set 1 of 3')

      // Set info should be smaller text
      expect(setInfo.classList.contains('text-sm')).toBe(true)

      // Both should be in a vertical container - checking the actual parent structure
      const container = exerciseName.parentElement?.parentElement
      expect(container?.classList.contains('flex-col')).toBe(true)
    })
  })

  it('renders without back button by default', () => {
    render(<IOSNavigationBar title="Test Title" />)

    expect(screen.queryByLabelText('Go back')).not.toBeInTheDocument()
  })

  it('renders with back button when showBackButton is true', () => {
    render(<IOSNavigationBar title="Test Title" showBackButton />)

    expect(screen.getByLabelText('Go back')).toBeInTheDocument()
  })

  it('calls onBackClick when back button is clicked', async () => {
    const handleBackClick = vi.fn()
    render(
      <IOSNavigationBar
        title="Test Title"
        showBackButton
        onBackClick={handleBackClick}
      />
    )

    await userEvent.click(screen.getByLabelText('Go back'))

    expect(handleBackClick).toHaveBeenCalledTimes(1)
  })

  it('renders right element when provided', () => {
    const rightElement = <button>Action</button>
    render(<IOSNavigationBar title="Test Title" rightElement={rightElement} />)

    expect(screen.getByText('Action')).toBeInTheDocument()
  })

  it('has correct height (66px - 50% taller than original)', () => {
    const { container } = render(<IOSNavigationBar title="Test Title" />)
    const header = container.querySelector('header')
    const headerContent = header?.querySelector('.h-\\[66px\\]')

    expect(headerContent).toBeInTheDocument()
    // h-[66px] is exactly 66px (44px * 1.5 = 66px for 50% increase)
  })

  it('has fixed positioning and proper z-index', () => {
    const { container } = render(<IOSNavigationBar title="Test Title" />)
    const header = container.querySelector('header')

    expect(header?.classList.contains('fixed')).toBe(true)
    expect(header?.classList.contains('top-0')).toBe(true)
    expect(header?.classList.contains('left-0')).toBe(true)
    expect(header?.classList.contains('right-0')).toBe(true)
    expect(header?.classList.contains('z-50')).toBe(true)
  })

  it('has three-section layout with flexbox', () => {
    const { container } = render(<IOSNavigationBar title="Test Title" />)
    const sections = container.querySelectorAll('.flex-1')

    expect(sections).toHaveLength(3)
  })

  it('applies custom className', () => {
    const { container } = render(
      <IOSNavigationBar title="Test Title" className="custom-class" />
    )
    const header = container.querySelector('header')

    expect(header?.classList.contains('custom-class')).toBe(true)
  })

  it('renders chevron icon in back button', () => {
    const { container } = render(
      <IOSNavigationBar title="Test Title" showBackButton />
    )
    const svg = container.querySelector('svg')

    expect(svg).toBeInTheDocument()
    expect(svg?.getAttribute('width')).toBe('28')
    expect(svg?.getAttribute('height')).toBe('28')
  })

  it('truncates long titles', () => {
    const longTitle = 'This is a very long title that should be truncated'
    render(<IOSNavigationBar title={longTitle} />)

    const titleElement = screen.getByText(longTitle)
    expect(titleElement.classList.contains('truncate')).toBe(true)
  })

  it('includes spacer div to prevent content overlap', () => {
    const { container } = render(<IOSNavigationBar title="Test Title" />)
    const spacer = container.querySelector('div.h-\\[66px\\]')

    expect(spacer).toBeInTheDocument()
    expect(spacer?.nextElementSibling).toBeNull() // Should be last element
  })

  it('has iOS-style blur effects', () => {
    const { container } = render(<IOSNavigationBar title="Test Title" />)
    const header = container.querySelector('header')

    expect(header?.classList.contains('backdrop-blur-md')).toBe(true)
    expect(header?.classList.contains('bg-bg-primary/80')).toBe(true)
    // Component doesn't have border-b or border-brand-primary/10 classes
  })

  it('supports browsers without backdrop-filter', () => {
    const { container } = render(<IOSNavigationBar title="Test Title" />)
    const header = container.querySelector('header')

    // Background color classes provide fallback when backdrop-filter is not supported
    expect(header?.classList.contains('bg-bg-primary/80')).toBe(true)
  })

  it('has dark mode support for all elements', () => {
    render(<IOSNavigationBar title="Test Title" showBackButton />)

    // Check title has theme-aware text color
    const titleElement = screen.getByText('Test Title')
    expect(titleElement.classList.contains('text-text-primary')).toBe(true)

    // Check back button has theme-aware hover state
    const backButton = screen.getByLabelText('Go back')
    expect(backButton.classList.contains('hover:bg-bg-secondary')).toBe(true)
  })

  describe('Element Sizing', () => {
    it('has title with text-lg size for better proportions', () => {
      render(<IOSNavigationBar title="Test Title" />)
      const titleElement = screen.getByText('Test Title')
      expect(titleElement.classList.contains('text-lg')).toBe(true)
    })

    it('has back button icon sized at 28px for better proportions', () => {
      const { container } = render(
        <IOSNavigationBar title="Test Title" showBackButton />
      )
      const svg = container.querySelector('svg')
      expect(svg?.getAttribute('width')).toBe('28')
      expect(svg?.getAttribute('height')).toBe('28')
    })

    it('renders custom right element icons at appropriate size', () => {
      const rightElement = (
        <button>
          <svg width="24" height="24" viewBox="0 0 24 24">
            <circle cx="12" cy="12" r="2" />
          </svg>
        </button>
      )
      const { container } = render(
        <IOSNavigationBar title="Test Title" rightElement={rightElement} />
      )
      const svg = container.querySelector('svg')
      expect(svg?.getAttribute('width')).toBe('24')
      expect(svg?.getAttribute('height')).toBe('24')
    })
  })

  describe('Old Exercise Page Behavior', () => {
    it('should hide setInfo when on old exercise page route', () => {
      // FAILING TEST: This test should fail initially to follow TDD
      mockUsePathname.mockReturnValue('/workout/exercise/123')

      render(
        <IOSNavigationBar
          title="Test Exercise"
          isExercisePage
          setInfo="Set 1 of 8"
        />
      )

      // setInfo should NOT be displayed on old exercise pages
      expect(screen.queryByText('Set 1 of 8')).not.toBeInTheDocument()
    })

    it('should not render the info container div on old exercise pages', () => {
      mockUsePathname.mockReturnValue('/workout/exercise/123')

      const { container } = render(
        <IOSNavigationBar title="Test Exercise" isExercisePage setInfo="" />
      )

      // The entire info container should not be rendered
      const infoContainer = container.querySelector(
        '.flex.items-center.gap-2.mt-1'
      )
      expect(infoContainer).not.toBeInTheDocument()
    })

    it('should hide progress bar when on old exercise page route', () => {
      // FAILING TEST: This test should fail initially to follow TDD
      mockUsePathname.mockReturnValue('/workout/exercise/456')

      render(
        <IOSNavigationBar
          title="Test Exercise"
          isExercisePage
          progressValue={50}
          totalSets={8}
          completedSets={4}
        />
      )

      // Progress bar should NOT be displayed on old exercise pages
      expect(screen.queryByTestId('nav-progress-bar')).not.toBeInTheDocument()
    })

    it('should show setInfo when on v2 exercise page route', () => {
      mockUsePathname.mockReturnValue('/workout/exercise-v2/123')

      render(
        <IOSNavigationBar
          title="Test Exercise"
          isExercisePage
          setInfo="Set 2 of 6"
        />
      )

      // setInfo SHOULD be displayed on v2 exercise pages
      expect(screen.getByText('Set 2 of 6')).toBeInTheDocument()
    })

    it('should show progress bar when on v2 exercise page route', () => {
      mockUsePathname.mockReturnValue('/workout/exercise-v2/456')

      render(
        <IOSNavigationBar
          title="Test Exercise"
          isExercisePage
          progressValue={75}
          totalSets={8}
          completedSets={6}
        />
      )

      // Progress bar SHOULD be displayed on v2 exercise pages
      expect(screen.getByTestId('nav-progress-bar')).toBeInTheDocument()
    })

    it('should show setInfo when not on exercise page route', () => {
      mockUsePathname.mockReturnValue('/some/other/route')

      render(
        <IOSNavigationBar
          title="Test Exercise"
          isExercisePage
          setInfo="Set 3 of 4"
        />
      )

      // setInfo should be displayed when not on old exercise route
      expect(screen.getByText('Set 3 of 4')).toBeInTheDocument()
    })

    it('should show progress bar when not on exercise page route', () => {
      mockUsePathname.mockReturnValue('/some/other/route')

      render(
        <IOSNavigationBar
          title="Test Exercise"
          isExercisePage
          progressValue={25}
          totalSets={4}
          completedSets={1}
        />
      )

      // Progress bar should be displayed when not on old exercise route
      expect(screen.getByTestId('nav-progress-bar')).toBeInTheDocument()
    })
  })
})

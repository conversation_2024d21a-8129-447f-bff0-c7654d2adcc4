'use client'

import { useEffect, useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import type {
  ExerciseModel,
  WorkoutLogSerieModel,
  RecommendationModel,
} from '@/types'
import { useSwipeAnimation } from '@/hooks/useSwipeAnimation'
import { vibrate } from '@/utils/haptics'
import { SetMetricsDisplay } from './SetMetricsDisplay'
import { CurrentSetCardControls } from './CurrentSetCardControls'

interface CurrentSetCardProps {
  exercise: ExerciseModel | null
  currentSet: WorkoutLogSerieModel | null
  setData: { reps: number; weight: number; duration: number }
  onSetDataChange: (data: {
    reps: number
    weight: number
    duration: number
  }) => void
  onComplete: () => void
  onSkip: () => void
  isSaving: boolean
  completedSets: number
  unit: 'kg' | 'lbs'
  recommendation?: RecommendationModel | null
  currentSetIndex?: number
  isWarmup?: boolean
  isFirstWorkSet?: boolean
}

export function CurrentSetCard({
  exercise,
  currentSet,
  setData,
  onSetDataChange,
  onComplete,
  onSkip,
  isSaving,
  completedSets,
  unit,
  recommendation = null,
  currentSetIndex = 0,
  isWarmup = false,
  isFirstWorkSet = false,
}: CurrentSetCardProps) {
  const [isAnimating, setIsAnimating] = useState(false)
  const [animationClass, setAnimationClass] = useState('')

  // Initialize swipe animation hook
  const {
    controls,
    isDragging,
    handleDragEnd,
    handleDragStart,
    triggerFadeAnimation,
  } = useSwipeAnimation({
    onComplete,
    onSkip,
  })

  // Cleanup animation timers on unmount
  useEffect(() => {
    return () => {
      // Clear any pending animation timers
      setAnimationClass('')
    }
  }, [])

  // Set metrics will be handled by SetMetricsDisplay component

  // Pre-populate with last set's data or recommendation
  useEffect(() => {
    if (currentSet && !setData.reps && !setData.weight) {
      onSetDataChange({
        reps: currentSet.Reps || 0,
        weight: currentSet.Weight?.[unit === 'kg' ? 'Kg' : 'Lb'] || 0,
        duration: setData.duration,
      })
    }
  }, [currentSet, setData, onSetDataChange, unit])
  // Handle save button click with fade animation
  const handleSaveClick = useCallback(async () => {
    if (isAnimating || isSaving) return

    setIsAnimating(true)
    setAnimationClass('fade-out') // Add fade-out first
    vibrate('success')

    // Trigger fade animation first
    await triggerFadeAnimation()

    // Then call onComplete
    onComplete()

    // After save, trigger fade-in for new controls
    setTimeout(() => {
      setAnimationClass('fade-in')
    }, 100)

    setIsAnimating(false)
  }, [isAnimating, isSaving, triggerFadeAnimation, onComplete])

  if (!currentSet || !exercise) return null

  // For warmup sets, use currentSetIndex to determine the warmup number
  const setLabel = isWarmup
    ? `Warm-up ${currentSetIndex + 1}`
    : `Set ${completedSets + 1}`

  return (
    <div className="w-full max-w-md">
      {/* Main card */}
      <motion.div
        drag="x"
        dragConstraints={{ left: -150, right: 150 }}
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        animate={controls}
        className={`
          bg-surface-secondary rounded-2xl p-4 shadow-lg
          ${isDragging ? 'cursor-grabbing' : 'cursor-grab'}
          ${isSaving ? 'opacity-50' : ''}
        `}
        data-testid="current-set-card"
      >
        {/* Set label */}
        <div className="text-center mb-2">
          <h2 className="text-2xl font-bold text-text-primary">{setLabel}</h2>
          {isWarmup && (
            <p className="text-sm text-text-secondary mt-1">
              Prepare your muscles
            </p>
          )}
        </div>

        {/* Input controls - arrow layout */}
        <CurrentSetCardControls
          setData={setData}
          onSetDataChange={onSetDataChange}
          unit={unit}
          isSaving={isSaving}
          animationClass={animationClass}
        />

        {/* Set metrics (Last time and 1RM Progress) - moved below controls */}
        <SetMetricsDisplay
          recommendation={recommendation}
          currentSetIndex={currentSetIndex}
          isWarmup={isWarmup}
          isFirstWorkSet={isFirstWorkSet}
          unit={unit}
          currentReps={setData.reps}
          currentWeight={setData.weight}
        />

        {/* Swipe hint */}
        <div className="mt-4 text-center">
          <p className="text-sm text-text-secondary">
            Swipe left to skip · right to complete
          </p>
        </div>
      </motion.div>

      {/* Quick action button (fallback for non-swipe) */}
      <div className="w-full mt-4">
        <button
          onClick={handleSaveClick}
          disabled={isSaving || isAnimating}
          className={`w-full py-4 min-h-[56px] rounded-theme font-semibold text-lg transition-all ${
            isSaving || isAnimating
              ? 'bg-bg-tertiary text-text-tertiary cursor-not-allowed opacity-60'
              : 'bg-gradient-metallic-gold text-text-inverse shadow-theme-xl hover:shadow-theme-2xl active:scale-[0.98] shimmer-hover text-shadow-sm'
          }`}
        >
          {isSaving ? 'Saving...' : 'Save set'}
        </button>
      </div>
    </div>
  )
}
